import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { showToast, GlobalContext } from "Context/Global";

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

const NotesPage = () => {
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [activeTab, setActiveTab] = useState("notes");
  const [notes, setNotes] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredNotes, setFilteredNotes] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  // Note creation states
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [newNoteContent, setNewNoteContent] = useState("");
  // Task creation states
  const [isAddingTask, setIsAddingTask] = useState(false);
  const [newTaskData, setNewTaskData] = useState({
    content: "",
    due_date: ""
  });
  // Edit states
  const [editingNote, setEditingNote] = useState(null);
  const [editingTask, setEditingTask] = useState(null);
  const [editNoteContent, setEditNoteContent] = useState("");
  const [editTaskData, setEditTaskData] = useState({
    content: "",
    due_date: ""
  });
  // Delete confirmation states
  const [deleteConfirmation, setDeleteConfirmation] = useState({
    show: false,
    type: null, // 'note' or 'task'
    item: null
  });

  useEffect(() => {
    loadNotes();
    loadTasks();
  }, []);

  useEffect(() => {
    if (notes.length > 0 || tasks.length > 0) {
      filterNotes();
      filterTasks();
    }
  }, [searchQuery, notes, tasks]);

  const loadNotes = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/notes",
        {},
        "GET"
      );

      if (!response.error) {
        // Sort notes by created_at in descending order (latest first)
        const sortedNotes = (response.list || []).sort((a, b) => {
          return new Date(b.created_at) - new Date(a.created_at);
        });
        setNotes(sortedNotes);
        setFilteredNotes(sortedNotes);
      }
    } catch (err) {
      console.error("Failed to load notes:", err);
      showToast(globalDispatch, err.message || "Failed to load notes", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const loadTasks = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/tasks",
        {},
        "GET"
      );

      if (!response.error) {
        // Sort tasks by due_date in ascending order (closest due date first)
        const sortedTasks = (response.list || []).sort((a, b) => {
          const dueDateA = a.due_date || a.event_details?.value?.due_date || a.due_date?.value;
          const dueDateB = b.due_date || b.event_details?.value?.due_date || b.due_date?.value;
          if (!dueDateA && !dueDateB) return 0;
          if (!dueDateA) return 1;
          if (!dueDateB) return -1;
          return new Date(dueDateA) - new Date(dueDateB);
        });
        setTasks(sortedTasks);
        setFilteredTasks(sortedTasks);
      }
    } catch (err) {
      console.error("Failed to load tasks:", err);
      showToast(globalDispatch, err.message || "Failed to load tasks", 5000, "error");
    }
  };

  const filterNotes = () => {
    if (!searchQuery.trim()) {
      setFilteredNotes(notes);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = notes.filter(note =>
      note.description.toLowerCase().includes(query) ||
      note.title.toLowerCase().includes(query) ||
      (note.referral_title && note.referral_title.toLowerCase().includes(query)) ||
      (note.referral_description && note.referral_description.toLowerCase().includes(query))
    );

    setFilteredNotes(filtered);
  };

  const filterTasks = () => {
    if (!searchQuery.trim()) {
      setFilteredTasks(tasks);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = tasks.filter(task => {
      const content = task.description || task.event_details?.value?.content || task.content?.value || task.content || "";
      const referralTitle = task.referral_title || "";
      const referralDescription = task.referral_description || "";

      return content.toLowerCase().includes(query) ||
             referralTitle.toLowerCase().includes(query) ||
             referralDescription.toLowerCase().includes(query);
    });

    setFilteredTasks(filtered);
  };

  const handleAddNote = async () => {
    if (!newNoteContent.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/notes",
        {
          content: { value: newNoteContent }
        },
        "POST"
      );

      if (!response.error) {
        // Create a new note with current timestamp while waiting for API response
        const timestamp = new Date().toISOString();
        const newNote = {
          id: response.model?.id?.value || Date.now(),
          description: newNoteContent,
          created_at: response.model?.created_at?.value || timestamp,
        };

        // Add new note at the beginning of the array
        setNotes(prev => [newNote, ...prev]);
        setNewNoteContent("");
        setIsAddingNote(false);

        // Refresh notes to get the correct data from server
        loadNotes();

        showToast(globalDispatch, "Note added successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to add note");
      }
    } catch (err) {
      console.error("Error adding note:", err);
      showToast(globalDispatch, err.message || "Failed to add note", 5000, "error");
    }
  };

  const showDeleteConfirmation = (type, item) => {
    setDeleteConfirmation({
      show: true,
      type: type,
      item: item
    });
  };

  const hideDeleteConfirmation = () => {
    setDeleteConfirmation({
      show: false,
      type: null,
      item: null
    });
  };

  const handleDeleteNote = async (noteId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes/${noteId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        // Remove the deleted note from the list
        setNotes(prev => prev.filter(note => note.id !== noteId));
        showToast(globalDispatch, "Note deleted successfully!", 5000, "success");
        hideDeleteConfirmation();
      } else {
        throw new Error(response.message || "Failed to delete note");
      }
    } catch (err) {
      console.error("Error deleting note:", err);
      showToast(globalDispatch, err.message || "Failed to delete note", 5000, "error");
    }
  };

  const handleAddTask = async () => {
    if (!newTaskData.content.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/tasks",
        {
          description: { value: newTaskData.content },
          due_date: { value: newTaskData.due_date }
        },
        "POST"
      );

      if (!response.error) {
        // Create a new task with current timestamp while waiting for API response
        const timestamp = new Date().toISOString();
        const newTask = {
          id: response.model?.id?.value || Date.now(),
          content: newTaskData.content,
          due_date: newTaskData.due_date,
          created_at: response.model?.created_at?.value || timestamp,
        };

        // Add new task to the list
        setTasks(prev => [newTask, ...prev]);
        setNewTaskData({ content: "", due_date: "" });
        setIsAddingTask(false);

        // Refresh tasks to get the correct data from server
        loadTasks();

        showToast(globalDispatch, "Task added successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to add task");
      }
    } catch (err) {
      console.error("Error adding task:", err);
      showToast(globalDispatch, err.message || "Failed to add task", 5000, "error");
    }
  };

  const handleDeleteTask = async (taskId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/tasks/${taskId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        // Remove the deleted task from the list
        setTasks(prev => prev.filter(task => task.id !== taskId));
        showToast(globalDispatch, "Task deleted successfully!", 5000, "success");
        hideDeleteConfirmation();
      } else {
        throw new Error(response.message || "Failed to delete task");
      }
    } catch (err) {
      console.error("Error deleting task:", err);
      showToast(globalDispatch, err.message || "Failed to delete task", 5000, "error");
    }
  };

  const handleEditNote = async () => {
    if (!editNoteContent.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes/${editingNote.id}`,
        {
          content: { value: editNoteContent }
        },
        "POST"
      );

      if (!response.error) {
        setEditingNote(null);
        setEditNoteContent("");
        loadNotes();
        showToast(globalDispatch, "Note updated successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to update note");
      }
    } catch (err) {
      console.error("Error updating note:", err);
      showToast(globalDispatch, err.message || "Failed to update note", 5000, "error");
    }
  };

  const handleEditTask = async () => {
    if (!editTaskData.content.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/tasks/${editingTask.id}`,
        {
          description: { value: editTaskData.content },
          due_date: { value: editTaskData.due_date }
        },
        "POST"
      );

      if (!response.error) {
        setEditingTask(null);
        setEditTaskData({ content: "", due_date: "" });
        loadTasks();
        showToast(globalDispatch, "Task updated successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to update task");
      }
    } catch (err) {
      console.error("Error updating task:", err);
      showToast(globalDispatch, err.message || "Failed to update task", 5000, "error");
    }
  };

  const startEditingNote = (note) => {
    setEditingNote(note);
    setEditNoteContent(note.description || note.content?.value || note.content || "");
  };

  const startEditingTask = (task) => {
    setEditingTask(task);
    const content = task.description || task.event_details?.value?.content || task.content?.value || task.content || "";
    const dueDate = task.due_date || task.event_details?.value?.due_date || task.due_date?.value || "";

    // Convert due date to datetime-local format if needed
    let formattedDueDate = "";
    if (dueDate) {
      const date = new Date(dueDate);
      if (!isNaN(date.getTime())) {
        formattedDueDate = date.toISOString().slice(0, 16);
      }
    }

    setEditTaskData({
      content: content,
      due_date: formattedDueDate
    });
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#eaeaea]">My Notes & Tasks</h1>
        <p className="text-[#b5b5b5]">
          Create and manage your personal notes and tasks here.
        </p>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <div className="flex border-b border-[#363636]">
          <button
            onClick={() => setActiveTab("notes")}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "notes"
                ? "border-[#2e7d32] text-[#eaeaea]"
                : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
            }`}
          >
            Notes ({notes.length})
          </button>
          <button
            onClick={() => setActiveTab("tasks")}
            className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "tasks"
                ? "border-[#2e7d32] text-[#eaeaea]"
                : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
            }`}
          >
            Tasks ({tasks.length})
          </button>
        </div>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <div className="relative w-64">
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder={`Search ${activeTab}...`}
            className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] pl-4 pr-10 text-[#eaeaea]"
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-[#b5b5b5]">
            <svg className="h-4 w-4" viewBox="0 0 24 24" fill="none">
              <path
                d="M15.7955 15.8111L21 21M18 10.5C18 14.6421 14.6421 18 10.5 18C6.35786 18 3 14.6421 3 10.5C3 6.35786 6.35786 3 10.5 3C14.6421 3 18 6.35786 18 10.5Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>

        <button
          onClick={() => activeTab === "notes" ? setIsAddingNote(true) : setIsAddingTask(true)}
          className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-white hover:bg-[#1b5e20] transition-colors"
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Add {activeTab === "notes" ? "Note" : "Task"}
        </button>
      </div>

      {/* Note creation form */}
      {isAddingNote && activeTab === "notes" && (
        <div className="mb-6 rounded-lg border border-[#363636] bg-[#242424] p-4">
          <h3 className="mb-3 text-lg font-medium text-[#eaeaea]">Add New Note</h3>
          <textarea
            value={newNoteContent}
            onChange={(e) => setNewNoteContent(e.target.value)}
            placeholder="Write your note..."
            className="mb-3 w-full rounded border border-[#363636] bg-[#161616] p-3 text-[#eaeaea] placeholder-[#666] resize-none"
            rows={4}
          />
          <div className="flex justify-end gap-2">
            <button
              onClick={() => {
                setIsAddingNote(false);
                setNewNoteContent("");
              }}
              className="rounded px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea] transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleAddNote}
              disabled={!newNoteContent.trim()}
              className="rounded bg-[#2e7d32] px-4 py-2 text-sm text-white hover:bg-[#1b5e20] disabled:bg-[#363636] disabled:text-[#666] transition-colors"
            >
              Add Note
            </button>
          </div>
        </div>
      )}

      {/* Task creation form */}
      {isAddingTask && activeTab === "tasks" && (
        <div className="mb-6 rounded-lg border border-[#363636] bg-[#242424] p-4">
          <h3 className="mb-3 text-lg font-medium text-[#eaeaea]">Add New Task</h3>
          <div className="space-y-3">
            <textarea
              value={newTaskData.content}
              onChange={(e) => setNewTaskData(prev => ({ ...prev, content: e.target.value }))}
              placeholder="Enter task description..."
              className="w-full rounded border border-[#363636] bg-[#161616] p-3 text-[#eaeaea] placeholder-[#666] resize-none"
              rows={4}
            />
            <input
              type="datetime-local"
              value={newTaskData.due_date}
              onChange={(e) => setNewTaskData(prev => ({ ...prev, due_date: e.target.value }))}
              className="w-full rounded border border-[#363636] bg-[#161616] p-3 text-[#eaeaea]"
            />
          </div>
          <div className="flex justify-end gap-2 mt-3">
            <button
              onClick={() => {
                setIsAddingTask(false);
                setNewTaskData({ content: "", due_date: "" });
              }}
              className="rounded px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea] transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleAddTask}
              disabled={!newTaskData.content.trim()}
              className="rounded bg-[#2e7d32] px-4 py-2 text-sm text-white hover:bg-[#1b5e20] disabled:bg-[#363636] disabled:text-[#666] transition-colors"
            >
              Add Task
            </button>
          </div>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-[#2e7d32] border-t-transparent"></div>
        </div>
      ) : activeTab === "notes" ? (
        filteredNotes.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredNotes.map((note) => (
              <div
                key={note.id}
                className="rounded-lg border border-[#363636] bg-[#242424] p-4"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    {editingNote && editingNote.id === note.id ? (
                      <div className="space-y-3">
                        <textarea
                          value={editNoteContent}
                          onChange={(e) => setEditNoteContent(e.target.value)}
                          className="w-full rounded border border-[#363636] bg-[#161616] p-3 text-[#eaeaea] placeholder-[#666] resize-none"
                          rows={4}
                        />
                        <div className="flex gap-2">
                          <button
                            onClick={handleEditNote}
                            disabled={!editNoteContent.trim()}
                            className="rounded bg-[#2e7d32] px-3 py-1 text-sm text-white hover:bg-[#1b5e20] disabled:bg-[#363636] disabled:text-[#666] transition-colors"
                          >
                            Save
                          </button>
                          <button
                            onClick={() => {
                              setEditingNote(null);
                              setEditNoteContent("");
                            }}
                            className="rounded px-3 py-1 text-sm text-[#b5b5b5] hover:text-[#eaeaea] transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <p className="text-[#eaeaea] whitespace-pre-wrap break-words">
                          {note.description}
                        </p>

                        {/* Display referral details if available */}
                        {note.referral_id && (
                          <div className="mt-3 p-2 rounded border-l-4 border-[#2e7d32] bg-[#1a1a1a]">
                            <div className="flex items-center justify-between mb-2">
                              <div>
                                <p className="text-xs text-[#b5b5b5] mb-1">Referral name</p>
                                <p className="text-sm text-[#eaeaea] font-medium">{note.referral_title}</p>
                              </div>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                note.referral_status === 'completed'
                                  ? 'bg-green-800 text-green-200'
                                  : note.referral_status === 'archived'
                                  ? 'bg-gray-700 text-gray-200'
                                  : note.referral_status === 'active'
                                  ? 'bg-blue-800 text-blue-200'
                                  : 'bg-yellow-800 text-yellow-200'
                              }`}>
                                {note.referral_status}
                              </span>
                            </div>
                            <button
                              onClick={() => navigate(`/member/referrals/${note.referral_id}/details`)}
                              className="flex items-center gap-1 text-xs text-[#7dd87d] hover:text-[#2e7d32] transition-colors"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                              View Referral
                            </button>
                          </div>
                        )}

                        <p className="mt-2 text-sm text-[#b5b5b5]">
                          {formatDate(note.created_at)}
                        </p>
                      </>
                    )}
                  </div>
                  {!editingNote || editingNote.id !== note.id ? (
                    <div className="flex gap-1 ml-2">
                      <button
                        onClick={() => startEditingNote(note)}
                        className="text-[#b5b5b5] hover:text-[#2e7d32]"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => showDeleteConfirmation('note', note)}
                        className="text-[#b5b5b5] hover:text-[#dc3545]"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  ) : null}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12">
            <svg className="h-16 w-16 text-[#363636]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <p className="mt-4 text-[#b5b5b5]">
              {searchQuery ? "No notes found matching your search." : "You don't have any notes yet. Create your first note!"}
            </p>
          </div>
        )
      ) : (
        filteredTasks.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTasks.map((task) => (
              <div
                key={task.id}
                className="rounded-lg border border-[#363636] bg-[#242424] p-4"
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    {editingTask && editingTask.id === task.id ? (
                      <div className="space-y-3">
                        <textarea
                          value={editTaskData.content}
                          onChange={(e) => setEditTaskData(prev => ({ ...prev, content: e.target.value }))}
                          className="w-full rounded border border-[#363636] bg-[#161616] p-3 text-[#eaeaea] placeholder-[#666] resize-none"
                          rows={4}
                        />
                        <input
                          type="datetime-local"
                          value={editTaskData.due_date}
                          onChange={(e) => setEditTaskData(prev => ({ ...prev, due_date: e.target.value }))}
                          className="w-full rounded border border-[#363636] bg-[#161616] p-3 text-[#eaeaea]"
                        />
                        <div className="flex gap-2">
                          <button
                            onClick={handleEditTask}
                            disabled={!editTaskData.content.trim()}
                            className="rounded bg-[#2e7d32] px-3 py-1 text-sm text-white hover:bg-[#1b5e20] disabled:bg-[#363636] disabled:text-[#666] transition-colors"
                          >
                            Save
                          </button>
                          <button
                            onClick={() => {
                              setEditingTask(null);
                              setEditTaskData({ content: "", due_date: "" });
                            }}
                            className="rounded px-3 py-1 text-sm text-[#b5b5b5] hover:text-[#eaeaea] transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <p className="text-[#eaeaea] whitespace-pre-wrap break-words mb-2">
                          {task.description || task.event_details?.value?.content || task.content?.value || task.content}
                        </p>

                        {(task.due_date || task.event_details?.value?.due_date || task.due_date?.value) && (
                          <p className="text-sm text-[#b5b5b5] mb-1">
                            Due: {formatDate(task.due_date || task.event_details?.value?.due_date || task.due_date?.value)}
                          </p>
                        )}

                        {/* Display referral details if available */}
                        {task.referral_id && (
                          <div className="mt-3 p-2 rounded border-l-4 border-[#2e7d32] bg-[#1a1a1a]">
                            <div className="flex items-center justify-between mb-2">
                              <div>
                                <p className="text-xs text-[#b5b5b5] mb-1">Referral name</p>
                                <p className="text-sm text-[#eaeaea] font-medium">{task.referral_title}</p>
                              </div>
                              <span className={`px-2 py-1 rounded text-xs font-medium ${
                                task.referral_status === 'completed'
                                  ? 'bg-green-800 text-green-200'
                                  : task.referral_status === 'archived'
                                  ? 'bg-gray-700 text-gray-200'
                                  : task.referral_status === 'active'
                                  ? 'bg-blue-800 text-blue-200'
                                  : 'bg-yellow-800 text-yellow-200'
                              }`}>
                                {task.referral_status}
                              </span>
                            </div>
                            <button
                              onClick={() => navigate(`/member/referrals/${task.referral_id}/details`)}
                              className="flex items-center gap-1 text-xs text-[#7dd87d] hover:text-[#2e7d32] transition-colors"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                              </svg>
                              View Referral
                            </button>
                          </div>
                        )}

                        <p className="mt-2 text-sm text-[#b5b5b5]">
                          Created: {formatDate(task.created_at?.value || task.created_at)}
                        </p>
                      </>
                    )}
                  </div>
                  {!editingTask || editingTask.id !== task.id ? (
                    <div className="flex gap-1 ml-2">
                      <button
                        onClick={() => startEditingTask(task)}
                        className="text-[#b5b5b5] hover:text-[#2e7d32]"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => showDeleteConfirmation('task', task)}
                        className="text-[#b5b5b5] hover:text-[#dc3545]"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  ) : null}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12">
            <svg className="h-16 w-16 text-[#363636]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m5 0h2a2 2 0 002-2V7a2 2 0 00-2-2h-2m-5 4v6m5-6v6"
              />
            </svg>
            <p className="mt-4 text-[#b5b5b5]">
              {searchQuery ? "No tasks found matching your search." : "You don't have any tasks yet. Create your first task!"}
            </p>
          </div>
        )
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirmation.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[#242424] rounded-lg p-6 max-w-md w-full mx-4 border border-[#363636]">
            <h3 className="text-lg font-medium text-[#eaeaea] mb-4">
              Confirm Delete
            </h3>
            <p className="text-[#b5b5b5] mb-6">
              Are you sure you want to delete this {deleteConfirmation.type}? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={hideDeleteConfirmation}
                className="px-4 py-2 text-[#b5b5b5] hover:text-[#eaeaea] transition-colors"
              >
                No
              </button>
              <button
                onClick={() => {
                  if (deleteConfirmation.type === 'note') {
                    handleDeleteNote(deleteConfirmation.item.id);
                  } else {
                    handleDeleteTask(deleteConfirmation.item.id);
                  }
                }}
                className="px-4 py-2 bg-[#dc3545] text-white rounded hover:bg-[#c82333] transition-colors"
              >
                Yes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotesPage;
