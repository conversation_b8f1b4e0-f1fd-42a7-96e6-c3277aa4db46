import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { showToast, GlobalContext } from "Context/Global";
import { Link } from "react-router-dom";

const formatDate = (dateString) => {
  // Fri, Nov 8, 11:00 AM
  const date = new Date(dateString);
  const month = date.toLocaleString('default', { month: 'short' });
  const day = date.getDate();
  return `${date.toLocaleString('default', { weekday: 'short' })}, ${month} ${day}, ${date.toLocaleString('default', { hour: 'numeric', minute: 'numeric', hour12: true })}`;
};

// Add Modal component at the top
const Modal = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative w-[500px] rounded-lg bg-[#242424] p-6">
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-[#b5b5b5] hover:text-[#eaeaea]"
        >
          <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        {children}
      </div>
    </div>
  );
};

const UserDashboardPage = () => {
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [activities, setActivities] = useState([ ]);
  const [filteredActivities, setFilteredActivities] = useState([ ]);

  const [notes, setNotes] = useState([ ]);

  const [tasks, setTasks] = useState([]);

  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Error state for handling API errors
  const [error, setError] = useState("");

  // Add these state variables
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [newNoteContent, setNewNoteContent] = useState("");

  // Add modal state
  const [isNoteModalOpen, setIsNoteModalOpen] = useState(false);
  const [selectedNote, setSelectedNote] = useState(null);

  // Add task modal state
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

console.log("notes",notes)

  const loadTasks = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/tasks",
        {},
        "GET"
      );

      if (!response.error) {
        // Sort tasks by due_date in ascending order (closest due date first)
        const sortedTasks = (response.list || []).sort((a, b) => {
          const dueDateA = a.due_date || a.event_details?.value?.due_date || a.due_date?.value;
          const dueDateB = b.due_date || b.event_details?.value?.due_date || b.due_date?.value;
          if (!dueDateA && !dueDateB) return 0;
          if (!dueDateA) return 1;
          if (!dueDateB) return -1;
          return new Date(dueDateA) - new Date(dueDateB);
        });
        // Only show first 5 tasks for dashboard
        setTasks(sortedTasks.slice(0, 5));
      }
    } catch (err) {
      console.error("Failed to load tasks:", err);
    }
  };

  const loadDashboardData = async () => {
    try {
      const sdk = new MkdSDK();
      const [activitiesRes, data] = await Promise.all([
        sdk.GetCommunityUpdates(),
        sdk.GetDashboardStats()
      ]);

      if (!activitiesRes.error && !data.error) {
        const {
          notes:notesRes = []
        } = data.data
        setActivities(activitiesRes.updates);
        setFilteredActivities(activitiesRes.updates);
        console.log("notesRes",notesRes)
        setNotes(notesRes);
      } else {
        throw new Error(activitiesRes.message || data.message);
      }

      // Load tasks separately
      await loadTasks();
    } catch (err) {
      setError(err.message);
      showToast(globalDispatch, err.message, 5000, "error");
      // setTimeout(() => setError(""), 5000);
    } finally {

    }
  };

  // Add debounce function
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };


  const getInitials = (name) => {
    console.log(name);
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const UserAvatar = ({ user }) => {
    if (user.author_photo?.value) {
      return (
        <img
          src={user.author_photo.value}
          alt={user.author.value}
          className="h-8 w-8 rounded-full object-cover"
        />
      );
    }

    return (
      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white">
        {getInitials(user.author.value)}
      </div>
    );
  };


  // Handle search with client-side filtering
  const handleSearch = (query) => {
    if (!query.trim()) {
      setIsSearching(false);
      setFilteredActivities(activities);
      return;
    }

    setIsSearching(true);

    // Filter activities based on search query
    const filtered = activities.filter(activity => {
      const searchTermLower = query.toLowerCase();

      // Search in title
      if (activity.title?.value?.toLowerCase().includes(searchTermLower)) {
        return true;
      }

      // Search in author
      if (activity.author?.value?.toLowerCase().includes(searchTermLower)) {
        return true;
      }

      // Search in type
      if (activity.type?.value?.toLowerCase().includes(searchTermLower)) {
        return true;
      }

      // Search in event details content if it exists
      if (activity.event_details?.value?.content?.toLowerCase().includes(searchTermLower)) {
        return true;
      }

      return false;
    });

    setFilteredActivities(filtered);
  };

  // Create debounced search function for client-side filtering
  const debouncedSearch = React.useCallback(
    debounce((query) => handleSearch(query), 300),
    [activities] // Add activities as dependency to update when activities change
  );

  // Handle input change
  const handleSearchInputChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    debouncedSearch(query);
  };



  // Add this helper function at the top of the component
  const highlightText = (text, query) => {
    if (!query.trim()) return text;

    console.log(text, query);
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text?.split(regex) || [];

    return parts.map((part, i) =>
      regex.test(part) ? (
        <span key={i} className="bg-[#2e7d32]/20 text-[#7dd87d]">{part}</span>
      ) : (
        part
      )
    );
  };



  // Add this function to handle note creation
  const handleAddNote = async () => {
    if (!newNoteContent.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.CreateNote({
        content: newNoteContent
      });

      if (!response.error) {
        setNotes(prev => [response.data, ...prev]);
        setNewNoteContent("");
        setIsAddingNote(false);
      }
    } catch (err) {
      setError(err.message);
      showToast(globalDispatch, err.message, 5000, "error");
      // setTimeout(() => setError(""), 5000);
    }
  };

  // Function to handle note deletion
  const handleDeleteNote = async (noteId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes/${noteId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        // Remove the deleted note from the list
        setNotes(prev => prev.filter(note => note.id.value !== noteId));
        showToast(globalDispatch, "Note deleted successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to delete note");
      }
    } catch (err) {
      console.error("Error deleting note:", err);
      showToast(globalDispatch, err.message || "Failed to delete note", 5000, "error");
    }
  };

  const handleActivityClick = (activity) => {
    if (activity.type.value.toLowerCase() === 'note') {
      // Show note in modal
      setSelectedNote(activity.event_details.value);
      setIsNoteModalOpen(true);
    } else if (activity.type.value.toLowerCase() === 'task') {
      // Show task in modal
      setSelectedTask(activity.event_details.value);
      setIsTaskModalOpen(true);
    } else if (activity.type.value.toLowerCase().includes('referral')) {
      // Navigate to single referral page
      navigate(`/member/referrals/${activity.reference_id.value}/details`);
    }
   else if (activity.type.value.toLowerCase().includes('community')) {
    // Navigate to referrals feed
    navigate('/member/communities', { state: { activeTab: 'referrals-feed', referralId: activity.reference_id.value } });
  }
  };

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-4 w-full">
      {/* Note Modal */}
      <Modal isOpen={isNoteModalOpen} onClose={() => setIsNoteModalOpen(false)}>
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-[#eaeaea]">Note Details</h3>
        </div>
        <div className="text-[#eaeaea]">
          {selectedNote?.content}
        </div>
      </Modal>

      {/* Task Modal */}
      <Modal isOpen={isTaskModalOpen} onClose={() => setIsTaskModalOpen(false)}>
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-[#eaeaea]">Task Details</h3>
        </div>
        <div className="text-[#eaeaea]">
          <p className="mb-2">{selectedTask?.content}</p>
          {selectedTask?.due_date && (
            <p className="text-sm text-[#b5b5b5]">
              Due: {formatDate(selectedTask.due_date)}
            </p>
          )}

        </div>
      </Modal>

      {/* {error && <Toast message={error} type="error" />} */}

      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            {!isSearching ? (
              <div>
                <p className="text-[#EAEAEA] text-[24px] font-bold">Welcome to RainmakerOS</p>
                <p className="text-[16px] text-[#B5B5B5]">
                  Your default community is Rain Maker LLC
                </p>
                <Link to='/member/communities'> <button className="text-[#7DD87D] text-[16px] underline">
                    Click here to view your community
                  </button></Link>
                </div>
              ) : (
                <h1 className="text-xl font-semibold text-[#eaeaea]">My Feed</h1>
              )}
          </div>
          <div className="flex items-center gap-4">
            <div className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={handleSearchInputChange}
                placeholder="Search..."
                className="h-[42px] w-64 border border-[#363636] bg-[#161616] pl-4 pr-10 text-[#eaeaea] placeholder-[#666]"
              />
              <div style={{
                top: "50%",
                right: "10px",
                transform: "translateY(-50%)",
              }} className="absolute right-3 text-white">
                <svg className="h-4 w-4" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M15.7955 15.8111L21 21M18 10.5C18 14.6421 14.6421 18 10.5 18C6.35786 18 3 14.6421 3 10.5C3 6.35786 6.35786 3 10.5 3C14.6421 3 18 6.35786 18 10.5Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>
            <button onClick={() => navigate("/member/referrals/add")} className="rounded bg-[#2e7d32] h-[42px] px-4 py-2 text-sm text-[#eaeaea]">
              + Post Opportunity
            </button>
          </div>
        </div>
      </div>

      {/* {searchError && <Toast message={searchError} />} */}

      {!isSearching ? (
        // Regular Dashboard View
        <div style={{
          width: "100%",
        }} className="flex gap-6">
          {/* Left Side - Community Updates */}
          <div style={{
            width: "65%",
          }} className="flex-1 bg-black rounded p-6">
            <h2 className="mb-4 text-xl font-bold text-[#eaeaea]">Community Updates</h2>
            <div className="space-y-4">
              {filteredActivities.length > 0 ? (
                filteredActivities.map((activity) => (
                  <div
                    key={activity.id.value}
                    className="rounded border border-[#363636] bg-[#242424] p-4"
                  >
                    <div className="mb-2">
                      <div className="flex items-center gap-4">
                        <UserAvatar user={activity} />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-[#eaeaea] mb-1 text-[16px]">
                                {searchQuery ? highlightText(activity.title.value, searchQuery) : activity.title.value}
                              </p>
                              <p className="text-sm text-[#b5b5b5]">
                                {searchQuery ? highlightText(activity.author.value, searchQuery) : activity.author.value} • {formatDate(activity.created_at.value)}
                              </p>
                            </div>
                            <div className="flex items-center gap-2 ml-4">
                              <button
                                onClick={() => handleActivityClick(activity)}
                                className="text-[#7dd87d] text-right text-[16px] hover:underline"
                              >
                                {activity.type.value.toLowerCase() !== 'recommendation' ?
                                  (searchQuery ?
                                    highlightText(activity.type.value.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '), searchQuery) :
                                    activity.type.value.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')) :
                                  ''}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-[#b5b5b5]">
                  No activities found matching your search.
                </div>
              )}
            </div>
          </div>

          {/* Right Side */}
          <div style={{
            width: "35%",
          }} className="space-y-6">
            {/* My Tasks */}
            <div className="p-4 bg-black rounded-[12px]">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-bold text-[#eaeaea]">My Tasks</h2>
                <Link to="/member/notes" className="text-[#7dd87d] text-sm hover:underline">
                  View All
                </Link>
              </div>

              <div className="space-y-4">
                {tasks.length > 0 ? (
                  tasks.map((task) => (
                    <div className="border-b border-[#363636] pb-4" key={task.id || task.id?.value}>
                      <p className="text-[#eaeaea] text-[16px]">
                        {task.content || task.content?.value || task.description || task.event_details?.value?.content}
                      </p>
                      <p className="text-[14px] text-[#b5b5b5]">
                        {task.due_date || task.due_date?.value || task.event_details?.value?.due_date ?
                          `Due: ${formatDate(task.due_date || task.due_date?.value || task.event_details?.value?.due_date)}` :
                          'No due date'
                        }
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-[#b5b5b5]">
                    No tasks found. <Link to="/member/notes" className="text-[#7dd87d] hover:underline">Create your first task</Link>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            <div className="rounded-xl bg-black p-4 mt-4">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-semibold text-[#eaeaea]">Notes</h2>
                <button
                  onClick={() => setIsAddingNote(true)}
                  className="flex h-6 w-6 items-center justify-center  text-[#7dd87d] text-2xl hover:bg-[#1b5e20]"
                >
                  <svg className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>

              {isAddingNote && (
                <div className="mb-4">
                  <textarea
                    value={newNoteContent}
                    onChange={(e) => setNewNoteContent(e.target.value)}
                    placeholder="Write your note..."
                    className="mb-2 w-full rounded border border-[#363636] bg-[#242424] p-2 text-[16px] text-[#eaeaea] placeholder-[#666]"
                    rows={3}
                  />
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => {
                        setIsAddingNote(false);
                        setNewNoteContent("");
                      }}
                      className="rounded px-3 py-1 text-sm text-[#b5b5b5] hover:text-[#eaeaea]"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleAddNote}
                      className="rounded bg-[#2e7d32] px-3 py-1 text-sm text-white hover:bg-[#1b5e20]"
                    >
                      Add Note
                    </button>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                {notes.map((note) => (
                  <div
                    key={note.id.value}
                    className="border-b border-[#363636] p-4"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-[16px] text-[#eaeaea]">{note.content.value}</p>
                        <p className="text-[14px] text-[#b5b5b5]">
                          {new Date(note.created_at.value).toLocaleString()}
                        </p>
                      </div>
                      <button
                        onClick={() => handleDeleteNote(note.id.value)}
                        className="text-[#b5b5b5] hover:text-[#dc3545]"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Search Results View
        <div style={{
          width: "100%",
        }} className="flex gap-6">
          <div style={{
            width: "65%",
          }} className="flex-1">
            <div className="mb-6 bg-black rounded p-6">
              <h2 className="mb-4 text-xl font-bold text-[#eaeaea]">Search Results</h2>
              <div className="space-y-4">
                {filteredActivities.length > 0 ? (
                  filteredActivities.map((activity) => (
                    <div
                      key={activity.id.value}
                      className="rounded border border-[#363636] bg-[#242424] p-4"
                    >
                      <div className="mb-2">
                        <div className="flex items-center gap-4">
                          <UserAvatar user={activity} />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="text-[#eaeaea] mb-1 text-[16px]">
                                  {highlightText(activity.title.value, searchQuery)}
                                </p>
                                <p className="text-sm text-[#b5b5b5]">
                                  {highlightText(activity.author.value, searchQuery)} • {formatDate(activity.created_at.value)}
                                </p>
                              </div>
                              <div className="flex items-center gap-2 ml-4">
                                <button
                                  onClick={() => handleActivityClick(activity)}
                                  className="text-[#7dd87d] text-right text-[16px] hover:underline"
                                >
                                  {activity.type.value.toLowerCase() !== 'recommendation' ?
                                    highlightText(activity.type.value.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '), searchQuery) :
                                    ''}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-[#b5b5b5]">
                    No activities found matching your search.
                  </div>
                )}
              </div>
            </div>
          </div>


          {/* Right Side stays the same */}
          <div style={{
            width: "35%",
          }} className="w-80 space-y-6">
            <div className="rounded p-4 bg-black">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-bold text-[#eaeaea]">My Tasks</h2>
                <Link to="/member/notes" className="text-[#7dd87d] text-sm hover:underline">
                  View All
                </Link>
              </div>
              <div className="space-y-4">
                {tasks.length > 0 ? (
                  tasks.map((task) => (
                    <div className="border-b border-[#363636] pb-4" key={task.id || task.id?.value}>
                      <p className="text-[#eaeaea] text-[16px]">
                        {task.content || task.content?.value || task.description || task.event_details?.value?.content}
                      </p>
                      <p className="text-[14px] text-[#b5b5b5]">
                        {task.due_date || task.due_date?.value || task.event_details?.value?.due_date ?
                          `Due: ${formatDate(task.due_date || task.due_date?.value || task.event_details?.value?.due_date)}` :
                          'No due date'
                        }
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4 text-[#b5b5b5]">
                    No tasks found. <Link to="/member/notes" className="text-[#7dd87d] hover:underline">Create your first task</Link>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            <div className="rounded-xl bg-black p-4 mt-4">
              <div className="mb-4 flex items-center justify-between">
                <h2 className="text-xl font-bold text-[#eaeaea]">Notes</h2>
                <button
                  onClick={() => setIsAddingNote(true)}
                  className="flex h-6 w-6 items-center justify-center  text-[#7dd87d] text-2xl hover:bg-[#1b5e20]"
                >
                  <svg className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </button>
              </div>

              {isAddingNote && (
                <div className="mb-4">
                  <textarea
                    value={newNoteContent}
                    onChange={(e) => setNewNoteContent(e.target.value)}
                    placeholder="Write your note..."
                    className="mb-2 w-full rounded border border-[#363636] bg-[#242424] p-2 text-sm text-[#eaeaea] placeholder-[#666]"
                    rows={3}
                  />
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => {
                        setIsAddingNote(false);
                        setNewNoteContent("");
                      }}
                      className="rounded px-3 py-1 text-sm text-[#b5b5b5] hover:text-[#eaeaea]"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleAddNote}
                      className="rounded bg-[#2e7d32] px-3 py-1 text-sm text-white hover:bg-[#1b5e20]"
                    >
                      Add Note
                    </button>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                {notes.map((note) => (
                  <div
                    key={note.id.value}
                    className="border-b border-[#363636] p-4"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-[16px] text-[#eaeaea]">{note.content.value}</p>
                        <p className="text-sm text-[#b5b5b5]">
                          {new Date(note.created_at.value).toLocaleString()}
                        </p>
                      </div>
                      <button
                        onClick={() => handleDeleteNote(note.id.value)}
                        className="text-[#b5b5b5] hover:text-[#dc3545]"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserDashboardPage;