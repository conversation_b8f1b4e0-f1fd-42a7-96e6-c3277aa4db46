import React, { useState, useEffect, useContext } from "react";
import { useParams, useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Context/Global";

// Local formatDate function
const formatDate = (dateString) => {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("default", { month: "short" });
  const year = date.getFullYear();
  return `${day} ${month}, ${year}`;
};

// Local UserAvatar component
const getInitials = (name) => {
  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

const UserAvatar = ({ user }) => {
  if (user?.photo?.value) {
    return (
      <img
        src={user.photo.value}
        alt={user.name?.value || "User"}
        className="object-cover w-8 h-8 rounded-full"
      />
    );
  }

  return (
    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white">
      {getInitials(user?.name?.value || "Unknown")}
    </div>
  );
};

// ReadMore component
const ReadMore = ({ text = '', maxWords = 100 }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Handle null/undefined/empty text
  if (!text) {
    return <p className="whitespace-pre-line text-[#b5b5b5]">No content available</p>;
  }

  const words = text.split(/\s+/);
  const needsReadMore = words.length > maxWords;

  const displayText = isExpanded ? text : words.slice(0, maxWords).join(' ') + (needsReadMore ? '...' : '');

  return (
    <div>
      <p className="whitespace-pre-line text-[#b5b5b5]">
        {displayText}
        {needsReadMore && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 text-[#7dd87d] hover:underline"
          >
            {isExpanded ? 'Read Less' : 'Read More'}
          </button>
        )}
      </p>
    </div>
  );
};

const SingleReferralPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  const [referral, setReferral] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState("details");
  const [notes, setNotes] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [isAddingTask, setIsAddingTask] = useState(false);
  const [newNoteContent, setNewNoteContent] = useState("");
  const [newTaskData, setNewTaskData] = useState({
    content: "",
    due_date: ""
  });
  // Edit states
  const [editingNote, setEditingNote] = useState(null);
  const [editingTask, setEditingTask] = useState(null);
  const [editNoteContent, setEditNoteContent] = useState("");
  const [editTaskData, setEditTaskData] = useState({
    content: "",
    due_date: ""
  });
  // Delete confirmation states
  const [deleteConfirmation, setDeleteConfirmation] = useState({
    show: false,
    type: null, // 'note' or 'task'
    item: null
  });

  useEffect(() => {
    if (id) {
      loadReferralDetails();
      loadNotes();
      loadTasks();
    }
  }, [id]);

  const loadReferralDetails = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.GetReferralDetail(id);

      if (!response.error && response.model) {
        setReferral(response.model);
      } else {
        setError(response.message || "Failed to load referral details");
      }
    } catch (err) {
      setError(err.message || "Failed to load referral details");
    } finally {
      setLoading(false);
    }
  };

  const loadNotes = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes?referral_id=${id}`,
        {},
        "GET"
      );

      if (!response.error) {
        setNotes(response.list || []);
      }
    } catch (err) {
      console.error("Failed to load notes:", err);
    }
  };

  const loadTasks = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/tasks?referral_id=${id}`,
        {},
        "GET"
      );

      if (!response.error) {
        setTasks(response.list || []);
      }
    } catch (err) {
      console.error("Failed to load tasks:", err);
    }
  };

  const handleAddNote = async () => {
    if (!newNoteContent.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/notes",
        {
          content: { value: newNoteContent },
          referral_id: { value: id }
        },
        "POST"
      );

      if (!response.error) {
        setNewNoteContent("");
        setIsAddingNote(false);
        loadNotes();
        showToast(globalDispatch, "Note added successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to add note");
      }
    } catch (err) {
      showToast(globalDispatch, err.message || "Failed to add note", 5000, "error");
    }
  };

  const handleAddTask = async () => {
    if (!newTaskData.content.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/tasks",
        {
          description: { value: newTaskData.content },
          due_date: { value: newTaskData.due_date },
          referral_id: { value: id }
        },
        "POST"
      );

      if (!response.error) {
        setNewTaskData({ content: "", due_date: "" });
        setIsAddingTask(false);
        loadTasks();
        showToast(globalDispatch, "Task added successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to add task");
      }
    } catch (err) {
      showToast(globalDispatch, err.message || "Failed to add task", 5000, "error");
    }
  };

  const showDeleteConfirmation = (type, item) => {
    setDeleteConfirmation({
      show: true,
      type: type,
      item: item
    });
  };

  const hideDeleteConfirmation = () => {
    setDeleteConfirmation({
      show: false,
      type: null,
      item: null
    });
  };

  const handleDeleteNote = async (noteId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes/${noteId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        loadNotes();
        showToast(globalDispatch, "Note deleted successfully!", 5000, "success");
        hideDeleteConfirmation();
      } else {
        throw new Error(response.message || "Failed to delete note");
      }
    } catch (err) {
      showToast(globalDispatch, err.message || "Failed to delete note", 5000, "error");
    }
  };

  const handleDeleteTask = async (taskId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/tasks/${taskId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        loadTasks();
        showToast(globalDispatch, "Task deleted successfully!", 5000, "success");
        hideDeleteConfirmation();
      } else {
        throw new Error(response.message || "Failed to delete task");
      }
    } catch (err) {
      showToast(globalDispatch, err.message || "Failed to delete task", 5000, "error");
    }
  };

  const handleEditNote = async () => {
    if (!editNoteContent.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes/${editingNote.id?.value || editingNote.id}`,
        {
          content: { value: editNoteContent }
        },
        "PUT"
      );

      if (!response.error) {
        setEditingNote(null);
        setEditNoteContent("");
        loadNotes();
        showToast(globalDispatch, "Note updated successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to update note");
      }
    } catch (err) {
      console.error("Error updating note:", err);
      showToast(globalDispatch, err.message || "Failed to update note", 5000, "error");
    }
  };

  const handleEditTask = async () => {
    if (!editTaskData.content.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/tasks/${editingTask.id?.value || editingTask.id}`,
        {
          description: { value: editTaskData.content },
          due_date: { value: editTaskData.due_date }
        },
        "PUT"
      );

      if (!response.error) {
        setEditingTask(null);
        setEditTaskData({ content: "", due_date: "" });
        loadTasks();
        showToast(globalDispatch, "Task updated successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to update task");
      }
    } catch (err) {
      console.error("Error updating task:", err);
      showToast(globalDispatch, err.message || "Failed to update task", 5000, "error");
    }
  };

  const startEditingNote = (note) => {
    setEditingNote(note);
    setEditNoteContent(note.content?.value || note.content || note.description?.value || note.description || "");
  };

  const startEditingTask = (task) => {
    setEditingTask(task);
    const content = task.description || task.event_details?.value?.content || task.content?.value || task.content || "";
    const dueDate = task.due_date || task.event_details?.value?.due_date || task.due_date?.value || "";

    // Convert due date to datetime-local format if needed
    let formattedDueDate = "";
    if (dueDate) {
      const date = new Date(dueDate);
      if (!isNaN(date.getTime())) {
        formattedDueDate = date.toISOString().slice(0, 16);
      }
    }

    setEditTaskData({
      content: content,
      due_date: formattedDueDate
    });
  };

  const renderReferralType = (referral) => {
    if (!referral.referral_type || !referral.referral_type.value) {
      return (
        <div className="flex gap-2 items-center">
          <span className="text-sm text-[#eaeaea]">Open Referral</span>
        </div>
      );
    }

    if (referral.referral_type.value === "community referral") {
      return (
        <div className="flex gap-2 items-center">
          <span className="text-sm text-[#eaeaea]">
            Community Referral to {referral.community?.title?.value || "Unknown Community"}
          </span>
        </div>
      );
    }

    if (referral.referral_type.value === "direct referral") {
      return (
        <div className="flex gap-2 items-center">
          <span className="text-sm text-[#eaeaea]">
            Direct Referral to {referral.referred_to?.name?.value || "Unknown Person"}
          </span>
        </div>
      );
    }

    return (
      <div className="flex gap-2 items-center">
        <span className="text-sm text-[#eaeaea]">Referral</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-[#eaeaea]">Loading referral details...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen">
        <div className="text-red-500 mb-4">{error}</div>
        <button
          onClick={() => navigate("/member/referrals")}
          className="px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]"
        >
          Back to Referrals
        </button>
      </div>
    );
  }

  if (!referral) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen">
        <div className="text-[#eaeaea] mb-4">Referral not found</div>
        <button
          onClick={() => navigate("/member/referrals")}
          className="px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]"
        >
          Back to Referrals
        </button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0a0a0a] p-4 md:p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => navigate("/member/referrals")}
            className="flex items-center gap-2 text-[#b5b5b5] hover:text-[#eaeaea] mb-4"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Referrals
          </button>

          <div className="bg-[#161616] rounded-xl p-6">
            <div className="flex justify-between items-start mb-4">
              <div className="flex gap-3">
                <UserAvatar user={referral.creator} />
                <div>
                  <h1 className="text-2xl font-bold text-[#eaeaea] mb-2">
                    {referral.title?.value || "Untitled Referral"}
                  </h1>
                  <div className="flex gap-4 items-center">
                    <p className="text-[#b5b5b5]">
                      {referral.creator?.name?.value || "Unknown"} - {formatDate(referral.created_at?.value)}
                    </p>
                    {renderReferralType(referral)}
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <span className="rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]">
                  Status: {referral.status?.value || "Unknown"}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-[#161616] rounded-xl">
          <div className="border-b border-[#363636]">
            <div className="flex">
              <button
                onClick={() => setActiveTab("details")}
                className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === "details"
                    ? "border-[#2e7d32] text-[#eaeaea]"
                    : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
                }`}
              >
                Details
              </button>
              <button
                onClick={() => setActiveTab("notes")}
                className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === "notes"
                    ? "border-[#2e7d32] text-[#eaeaea]"
                    : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
                }`}
              >
                Notes ({notes.length})
              </button>
              <button
                onClick={() => setActiveTab("tasks")}
                className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === "tasks"
                    ? "border-[#2e7d32] text-[#eaeaea]"
                    : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
                }`}
              >
                Tasks ({tasks.length})
              </button>
            </div>
          </div>

          <div className="p-6">
            {/* Details Tab */}
            {activeTab === "details" && (
              <div className="space-y-6">
                <div>
                  <h4 className="mb-2 font-medium text-[#eaeaea]">Type</h4>
                  <p className="text-[#b5b5b5]">
                    {referral?.type?.value
                      ? referral.type.value === "looking_for_service"
                        ? "Looking for Service"
                        : referral.type.value === "looking_for_product"
                        ? "Looking for Product"
                        : referral.type.value === "looking_for_buyer"
                        ? "Looking for Buyer"
                        : referral.type.value === "looking_for_investor"
                        ? "Looking for Investor"
                        : referral.type.value === "looking_for_partner"
                        ? "Looking for Partner"
                        : referral.type.value.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                      : 'N/A'}
                  </p>
                </div>

                <div>
                  <h4 className="mb-2 font-medium text-[#eaeaea]">Industry</h4>
                  <p className="text-[#b5b5b5]">{referral?.industry_name?.value || 'N/A'}</p>
                </div>

                <div>
                  <h4 className="mb-2 font-medium text-[#eaeaea]">Description</h4>
                  <ReadMore text={referral?.description?.value} maxWords={1000} />
                </div>

                <div>
                  <h4 className="mb-2 font-medium text-[#eaeaea]">Deal Size</h4>
                  <p className="text-[#b5b5b5]">${referral?.deal_size?.value || 'N/A'}</p>
                </div>

                <div>
                  <h4 className="mb-2 font-medium text-[#eaeaea]">Referral Fee</h4>
                  <p className="text-[#b5b5b5]">{referral?.referral_fee?.value || 'N/A'}%</p>
                </div>

                <div>
                  <h4 className="mb-2 font-medium text-[#eaeaea]">Payment Method</h4>
                  <p className="text-[#b5b5b5]">
                    {referral?.payment_method?.value
                      ? referral.payment_method.value === "bank"
                        ? "Bank Transfer"
                        : referral.payment_method.value === "stripe"
                        ? "Stripe"
                        : referral.payment_method.value.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                      : 'N/A'}
                  </p>
                </div>

                {referral?.additional_notes?.value && (
                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Additional Notes</h4>
                    <ReadMore text={referral.additional_notes.value} maxWords={500} />
                  </div>
                )}

                {referral?.expiration_date?.value && (
                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Expiration Date</h4>
                    <p className="text-[#b5b5b5]">{formatDate(referral.expiration_date.value)}</p>
                  </div>
                )}

                {referral?.description_image?.value && (
                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Attached Image</h4>
                    <img
                      src={referral.description_image.value}
                      alt="Description"
                      className="max-h-96 rounded-lg object-contain"
                    />
                  </div>
                )}
              </div>
            )}

            {/* Notes Tab */}
            {activeTab === "notes" && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-[#eaeaea]">Notes</h4>
                  <button
                    onClick={() => setIsAddingNote(true)}
                    className="px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]"
                  >
                    Add Note
                  </button>
                </div>

                {isAddingNote && (
                  <div className="bg-[#1e1e1e] p-4 rounded-lg border border-[#363636]">
                    <textarea
                      value={newNoteContent}
                      onChange={(e) => setNewNoteContent(e.target.value)}
                      placeholder="Enter your note..."
                      className="w-full h-24 bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea] placeholder-[#b5b5b5] resize-none"
                    />
                    <div className="flex gap-2 mt-3">
                      <button
                        onClick={handleAddNote}
                        className="px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]"
                      >
                        Save Note
                      </button>
                      <button
                        onClick={() => {
                          setIsAddingNote(false);
                          setNewNoteContent("");
                        }}
                        className="px-4 py-2 bg-[#363636] text-[#eaeaea] rounded-lg hover:bg-[#424242]"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}

                <div className="space-y-3">
                  {notes.length === 0 ? (
                    <p className="text-[#b5b5b5] text-center py-8">No notes yet</p>
                  ) : (
                    notes.map((note) => (
                      <div key={note.id} className="bg-[#1e1e1e] p-4 rounded-lg border border-[#363636]">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            {editingNote && (editingNote.id?.value || editingNote.id) === (note.id?.value || note.id) ? (
                              <div className="space-y-3">
                                <textarea
                                  value={editNoteContent}
                                  onChange={(e) => setEditNoteContent(e.target.value)}
                                  className="w-full h-24 bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea] placeholder-[#b5b5b5] resize-none"
                                />
                                <div className="flex gap-2">
                                  <button
                                    onClick={handleEditNote}
                                    disabled={!editNoteContent.trim()}
                                    className="px-3 py-1 bg-[#2e7d32] text-[#eaeaea] rounded hover:bg-[#1b5e20] disabled:bg-[#363636] disabled:text-[#666] text-sm"
                                  >
                                    Save
                                  </button>
                                  <button
                                    onClick={() => {
                                      setEditingNote(null);
                                      setEditNoteContent("");
                                    }}
                                    className="px-3 py-1 text-[#b5b5b5] hover:text-[#eaeaea] text-sm"
                                  >
                                    Cancel
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <p className="text-[#eaeaea] mb-2">
                                  {note.content?.value || note.content || note.description?.value || note.description}
                                </p>
                                <p className="text-sm text-[#b5b5b5]">
                                  {formatDate(note.created_at?.value || note.created_at)}
                                </p>
                              </>
                            )}
                          </div>
                          {!editingNote || (editingNote.id?.value || editingNote.id) !== (note.id?.value || note.id) ? (
                            <div className="flex gap-1 ml-2">
                              <button
                                onClick={() => startEditingNote(note)}
                                className="text-[#b5b5b5] hover:text-[#2e7d32]"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>
                              <button
                                onClick={() => showDeleteConfirmation('note', note)}
                                className="text-red-400 hover:text-red-300"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          ) : null}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}

            {/* Tasks Tab */}
            {activeTab === "tasks" && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-[#eaeaea]">Tasks</h4>
                  <button
                    onClick={() => setIsAddingTask(true)}
                    className="px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]"
                  >
                    Add Task
                  </button>
                </div>

                {isAddingTask && (
                  <div className="bg-[#1e1e1e] p-4 rounded-lg border border-[#363636]">
                    <div className="space-y-3">
                      <textarea
                        value={newTaskData.content}
                        onChange={(e) => setNewTaskData(prev => ({ ...prev, content: e.target.value }))}
                        placeholder="Enter task description..."
                        className="w-full h-20 bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea] placeholder-[#b5b5b5] resize-none"
                      />
                      <input
                        type="datetime-local"
                        value={newTaskData.due_date}
                        onChange={(e) => setNewTaskData(prev => ({ ...prev, due_date: e.target.value }))}
                        className="w-full bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea]"
                      />
                    </div>
                    <div className="flex gap-2 mt-3">
                      <button
                        onClick={handleAddTask}
                        className="px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]"
                      >
                        Save Task
                      </button>
                      <button
                        onClick={() => {
                          setIsAddingTask(false);
                          setNewTaskData({ content: "", due_date: "" });
                        }}
                        className="px-4 py-2 bg-[#363636] text-[#eaeaea] rounded-lg hover:bg-[#424242]"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}

                <div className="space-y-3">
                  {tasks.length === 0 ? (
                    <p className="text-[#b5b5b5] text-center py-8">No tasks yet</p>
                  ) : (
                    tasks.map((task) => (
                      <div key={task.id} className="bg-[#1e1e1e] p-4 rounded-lg border border-[#363636]">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            {editingTask && (editingTask.id?.value || editingTask.id) === (task.id?.value || task.id) ? (
                              <div className="space-y-3">
                                <textarea
                                  value={editTaskData.content}
                                  onChange={(e) => setEditTaskData(prev => ({ ...prev, content: e.target.value }))}
                                  className="w-full h-20 bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea] placeholder-[#b5b5b5] resize-none"
                                />
                                <input
                                  type="datetime-local"
                                  value={editTaskData.due_date}
                                  onChange={(e) => setEditTaskData(prev => ({ ...prev, due_date: e.target.value }))}
                                  className="w-full bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea]"
                                />
                                <div className="flex gap-2">
                                  <button
                                    onClick={handleEditTask}
                                    disabled={!editTaskData.content.trim()}
                                    className="px-3 py-1 bg-[#2e7d32] text-[#eaeaea] rounded hover:bg-[#1b5e20] disabled:bg-[#363636] disabled:text-[#666] text-sm"
                                  >
                                    Save
                                  </button>
                                  <button
                                    onClick={() => {
                                      setEditingTask(null);
                                      setEditTaskData({ content: "", due_date: "" });
                                    }}
                                    className="px-3 py-1 text-[#b5b5b5] hover:text-[#eaeaea] text-sm"
                                  >
                                    Cancel
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <>
                                <p className="text-[#eaeaea] mb-2">
                                  {task.description || task.event_details?.value?.content || task.content?.value || task.content}
                                </p>
                                {(task.due_date || task.event_details?.value?.due_date || task.due_date?.value) && (
                                  <p className="text-sm text-[#b5b5b5] mb-1">
                                    Due: {formatDate(task.due_date || task.event_details?.value?.due_date || task.due_date?.value)}
                                  </p>
                                )}
                                <p className="text-sm text-[#b5b5b5]">
                                  Created: {formatDate(task.created_at?.value || task.created_at)}
                                </p>
                              </>
                            )}
                          </div>
                          {!editingTask || (editingTask.id?.value || editingTask.id) !== (task.id?.value || task.id) ? (
                            <div className="flex gap-1 ml-2">
                              <button
                                onClick={() => startEditingTask(task)}
                                className="text-[#b5b5b5] hover:text-[#2e7d32]"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>
                              <button
                                onClick={() => showDeleteConfirmation('task', task)}
                                className="text-red-400 hover:text-red-300"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          ) : null}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteConfirmation.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-[#242424] rounded-lg p-6 max-w-md w-full mx-4 border border-[#363636]">
            <h3 className="text-lg font-medium text-[#eaeaea] mb-4">
              Confirm Delete
            </h3>
            <p className="text-[#b5b5b5] mb-6">
              Are you sure you want to delete this {deleteConfirmation.type}? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={hideDeleteConfirmation}
                className="px-4 py-2 text-[#b5b5b5] hover:text-[#eaeaea] transition-colors"
              >
                No
              </button>
              <button
                onClick={() => {
                  if (deleteConfirmation.type === 'note') {
                    handleDeleteNote(deleteConfirmation.item.id?.value || deleteConfirmation.item.id);
                  } else {
                    handleDeleteTask(deleteConfirmation.item.id?.value || deleteConfirmation.item.id);
                  }
                }}
                className="px-4 py-2 bg-[#dc3545] text-white rounded hover:bg-[#c82333] transition-colors"
              >
                Yes
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SingleReferralPage;
